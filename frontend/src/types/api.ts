// 通用 API 响应类型
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success?: boolean;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 错误响应类型
export interface ApiError {
  detail: string;
  status_code: number;
}

// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  is_active: boolean;
  is_superuser: boolean;
  preferences?: string;
  created_at: string;
  updated_at?: string;
  last_login?: string;
}

// 认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

// 项目相关类型
export interface Project {
  id: string;
  name: string;
  description?: string;
  database_type: string;
  database_host?: string;
  database_port?: string;
  database_name?: string;
  database_user?: string;
  database_url?: string;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at?: string;
}

export interface ProjectCreate {
  name: string;
  description?: string;
  database_type: string;
  database_host?: string;
  database_port?: string;
  database_name?: string;
  database_user?: string;
  database_password?: string;
  database_url?: string;
}

export interface ProjectUpdate {
  name?: string;
  description?: string;
  database_host?: string;
  database_port?: string;
  database_name?: string;
  database_user?: string;
  database_password?: string;
  database_url?: string;
  is_active?: boolean;
}
