// 训练相关类型定义

export enum TrainingType {
  TABLE_SCHEMA = 'TABLE_SCHEMA',
  SQL_EXAMPLES = 'SQL_EXAMPLES',
  DOCUMENTATION = 'DOCUMENTATION',
  MIXED = 'MIXED',
}

export enum TrainingStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export interface TrainingTask {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  training_type: TrainingType;
  training_config?: Record<string, any>;
  model_config?: Record<string, any>;
  status: TrainingStatus;
  progress_percentage?: number;
  current_step?: string;
  total_steps?: number;
  completed_steps?: number;
  training_score?: number;
  validation_score?: number;
  model_path?: string;
  error_message?: string;
  error_details?: Record<string, any>;
  created_by: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

export interface TrainingRecord {
  id: string;
  training_task_id: string;
  step_number: number;
  step_name?: string;
  step_description?: string;
  training_data?: Record<string, any>;
  training_result?: Record<string, any>;
  loss?: number;
  accuracy?: number;
  execution_time?: number;
  is_success: boolean;
  error_message?: string;
  created_at: string;
}

export interface SqlTraining {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  total_examples?: number;
  active_examples?: number;
  quality_score?: number;
  created_by: string;
  created_at: string;
  updated_at?: string;
}

export interface SqlExample {
  id: string;
  sql_training_id: string;
  question: string;
  sql_query: string;
  expected_result?: Record<string, any>;
  category?: string;
  difficulty?: string;
  tags?: string[];
  related_tables?: string[];
  quality_score?: number;
  is_verified: boolean;
  usage_count?: number;
  success_rate?: number;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at?: string;
  last_used?: string;
}

export interface TrainingTaskCreate {
  name: string;
  description?: string;
  training_type: TrainingType;
  training_config?: Record<string, any>;
  model_config?: Record<string, any>;
}

export interface TrainingTaskUpdate {
  name?: string;
  description?: string;
  training_config?: Record<string, any>;
  model_config?: Record<string, any>;
}

export interface SqlExampleCreate {
  question: string;
  sql_query: string;
  expected_result?: Record<string, any>;
  category?: string;
  difficulty?: string;
  tags?: string[];
  related_tables?: string[];
}

export interface SqlExampleUpdate {
  question?: string;
  sql_query?: string;
  expected_result?: Record<string, any>;
  category?: string;
  difficulty?: string;
  tags?: string[];
  related_tables?: string[];
  is_active?: boolean;
}

// 训练配置选项
export const TRAINING_DIFFICULTIES = ['easy', 'medium', 'hard'] as const;
export type TrainingDifficulty = typeof TRAINING_DIFFICULTIES[number];

export const TRAINING_CATEGORIES = [
  'basic_query',
  'join',
  'aggregation',
  'subquery',
  'window_function',
  'complex',
] as const;
export type TrainingCategory = typeof TRAINING_CATEGORIES[number];
