// 表相关类型定义

export interface ColumnSchema {
  id: string;
  column_name: string;
  display_name?: string;
  description?: string;
  data_type: string;
  max_length?: number;
  precision?: number;
  scale?: number;
  is_nullable: boolean;
  is_primary_key: boolean;
  is_unique: boolean;
  is_indexed: boolean;
  default_value?: string;
  sort_order: number;
  created_at: string;
  updated_at?: string;
}

export interface TableSchema {
  id: string;
  project_id: string;
  table_name: string;
  display_name?: string;
  description?: string;
  is_created: boolean;
  is_active: boolean;
  table_options?: Record<string, any>;
  row_count?: number;
  last_updated?: string;
  created_at: string;
  updated_at?: string;
  columns?: ColumnSchema[];
}

export interface ColumnCreate {
  column_name: string;
  display_name?: string;
  description?: string;
  data_type: string;
  max_length?: number;
  precision?: number;
  scale?: number;
  is_nullable?: boolean;
  is_primary_key?: boolean;
  is_unique?: boolean;
  is_indexed?: boolean;
  default_value?: string;
}

export interface TableCreate {
  table_name: string;
  display_name?: string;
  description?: string;
  columns: ColumnCreate[];
}

export interface TableUpdate {
  display_name?: string;
  description?: string;
  is_active?: boolean;
}

// 数据导入相关类型
export interface DataImport {
  id: string;
  table_id: string;
  file_name: string;
  file_path: string;
  file_size?: number;
  file_type?: string;
  import_config?: Record<string, any>;
  status: ImportStatus;
  total_rows?: number;
  success_rows?: number;
  failed_rows?: number;
  error_message?: string;
  progress_percentage?: number;
  current_row?: number;
  created_by: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

export enum ImportStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export interface ImportRecord {
  id: string;
  data_import_id: string;
  row_number: number;
  row_data?: Record<string, any>;
  is_success: boolean;
  error_message?: string;
  created_at: string;
}

export interface DataImportCreate {
  table_id: string;
  file_name: string;
  import_config?: Record<string, any>;
}

// 数据类型选项
export const DATA_TYPES = [
  'VARCHAR',
  'TEXT',
  'INTEGER',
  'BIGINT',
  'FLOAT',
  'DOUBLE',
  'DECIMAL',
  'BOOLEAN',
  'DATE',
  'DATETIME',
  'TIMESTAMP',
  'JSON',
  'UUID',
] as const;

export type DataType = typeof DATA_TYPES[number];
