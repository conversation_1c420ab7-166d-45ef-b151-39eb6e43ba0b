import React, { useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import AppHeader from './components/Layout/AppHeader'
import AppSider from './components/Layout/AppSider'
import Dashboard from './pages/Dashboard'
import TableManagement from './pages/TableManagement'
import DataImport from './pages/DataImport'
import TrainingManagement from './pages/TrainingManagement'
import TaskMonitor from './pages/TaskMonitor'
import Login from './pages/Login'
import PrivateRoute from './components/Auth/PrivateRoute'
import useAuthStore from './stores/authStore'

const { Content } = Layout

// 主布局组件
const MainLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Layout className="app-layout">
    <AppHeader />
    <Layout>
      <AppSider />
      <Layout>
        <Content className="app-content">
          {children}
        </Content>
      </Layout>
    </Layout>
  </Layout>
);

function App() {
  const { checkAuth } = useAuthStore();

  // 应用启动时检查认证状态
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return (
    <Routes>
      {/* 公开路由 */}
      <Route path="/login" element={<Login />} />

      {/* 受保护路由 */}
      <Route path="/" element={
        <PrivateRoute>
          <MainLayout>
            <Dashboard />
          </MainLayout>
        </PrivateRoute>
      } />

      <Route path="/tables" element={
        <PrivateRoute>
          <MainLayout>
            <TableManagement />
          </MainLayout>
        </PrivateRoute>
      } />

      <Route path="/data-import" element={
        <PrivateRoute>
          <MainLayout>
            <DataImport />
          </MainLayout>
        </PrivateRoute>
      } />

      <Route path="/training" element={
        <PrivateRoute>
          <MainLayout>
            <TrainingManagement />
          </MainLayout>
        </PrivateRoute>
      } />

      <Route path="/monitor" element={
        <PrivateRoute>
          <MainLayout>
            <TaskMonitor />
          </MainLayout>
        </PrivateRoute>
      } />

      {/* 未匹配的路由重定向到首页 */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  )
}

export default App
