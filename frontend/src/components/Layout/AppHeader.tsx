import React from 'react'
import { Layout, Typography, Space, Avatar, Dropdown, Button, Modal } from 'antd'
import { UserOutlined, SettingOutlined, LogoutOutlined } from '@ant-design/icons'
import type { MenuProps } from 'antd'
import useAuthStore from '../../stores/authStore'

const { Header } = Layout
const { Title } = Typography

const AppHeader: React.FC = () => {
  const { user, logout } = useAuthStore()

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ]

  const handleUserMenuClick: MenuProps['onClick'] = ({ key }) => {
    switch (key) {
      case 'profile':
        // TODO: 打开个人资料模态框
        break
      case 'settings':
        // TODO: 打开系统设置页面
        break
      case 'logout':
        Modal.confirm({
          title: '确认退出',
          content: '确定要退出登录吗？',
          onOk: () => {
            logout()
          },
        })
        break
    }
  }

  return (
    <Header className="app-header" style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: '0 24px'
    }}>
      <Space align="center">
        <div style={{
          width: 32,
          height: 32,
          background: '#1890ff',
          borderRadius: 8,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: 'bold'
        }}>
          V
        </div>
        <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
          Vanna Manager
        </Title>
      </Space>

      <Space>
        <Dropdown
          menu={{ items: userMenuItems, onClick: handleUserMenuClick }}
          placement="bottomRight"
        >
          <Button type="text" style={{ height: 'auto', padding: '8px 12px' }}>
            <Space>
              <Avatar size="small" icon={<UserOutlined />} />
              <span>{user?.full_name || user?.username || '用户'}</span>
            </Space>
          </Button>
        </Dropdown>
      </Space>
    </Header>
  )
}

export default AppHeader
