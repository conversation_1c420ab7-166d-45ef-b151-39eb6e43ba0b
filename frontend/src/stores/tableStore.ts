import { create } from 'zustand';
import { TableSchema, TableCreate, TableUpdate, ColumnSchema, DataImport } from '../types/table';
import tableService from '../services/tableService';

interface TableState {
  tables: TableSchema[];
  currentTable: TableSchema | null;
  columns: ColumnSchema[];
  tableData: any[];
  importHistory: DataImport[];
  isLoading: boolean;
  error: string | null;
  total: number;
  page: number;
  size: number;
  
  // Actions
  fetchTables: (projectId: string, page?: number, size?: number) => Promise<void>;
  fetchTable: (id: string) => Promise<void>;
  createTable: (projectId: string, table: TableCreate) => Promise<void>;
  updateTable: (id: string, table: TableUpdate) => Promise<void>;
  deleteTable: (id: string) => Promise<void>;
  setCurrentTable: (table: TableSchema | null) => void;
  fetchColumns: (tableId: string) => Promise<void>;
  fetchTablePreview: (tableId: string, limit?: number) => Promise<void>;
  fetchImportHistory: (tableId: string, page?: number, size?: number) => Promise<void>;
  createPhysicalTable: (id: string) => Promise<{ success: boolean; message: string }>;
  dropPhysicalTable: (id: string) => Promise<{ success: boolean; message: string }>;
  importData: (tableId: string, file: File, config?: Record<string, any>) => Promise<DataImport>;
}

const useTableStore = create<TableState>((set, get) => ({
  tables: [],
  currentTable: null,
  columns: [],
  tableData: [],
  importHistory: [],
  isLoading: false,
  error: null,
  total: 0,
  page: 1,
  size: 10,
  
  fetchTables: async (projectId: string, page = 1, size = 10) => {
    set({ isLoading: true, error: null });
    try {
      const response = await tableService.getTables(projectId, page, size);
      set({ 
        tables: response.items,
        total: response.total,
        page: response.page,
        size: response.size,
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '获取表列表失败' 
      });
    }
  },
  
  fetchTable: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const table = await tableService.getTable(id);
      set({ currentTable: table, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '获取表详情失败' 
      });
    }
  },
  
  createTable: async (projectId: string, tableData: TableCreate) => {
    set({ isLoading: true, error: null });
    try {
      const table = await tableService.createTable(projectId, tableData);
      const { tables } = get();
      set({ 
        tables: [table, ...tables],
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '创建表失败' 
      });
    }
  },
  
  updateTable: async (id: string, tableData: TableUpdate) => {
    set({ isLoading: true, error: null });
    try {
      const updatedTable = await tableService.updateTable(id, tableData);
      const { tables, currentTable } = get();
      
      set({ 
        tables: tables.map(t => t.id === id ? updatedTable : t),
        currentTable: currentTable?.id === id ? updatedTable : currentTable,
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '更新表失败' 
      });
    }
  },
  
  deleteTable: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      await tableService.deleteTable(id);
      const { tables, currentTable } = get();
      
      set({ 
        tables: tables.filter(t => t.id !== id),
        currentTable: currentTable?.id === id ? null : currentTable,
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '删除表失败' 
      });
    }
  },
  
  setCurrentTable: (table: TableSchema | null) => {
    set({ currentTable: table });
  },
  
  fetchColumns: async (tableId: string) => {
    set({ isLoading: true, error: null });
    try {
      const columns = await tableService.getColumns(tableId);
      set({ columns, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '获取列信息失败' 
      });
    }
  },
  
  fetchTablePreview: async (tableId: string, limit = 10) => {
    set({ isLoading: true, error: null });
    try {
      const data = await tableService.getTablePreview(tableId, limit);
      set({ tableData: data, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '获取表数据预览失败' 
      });
    }
  },
  
  fetchImportHistory: async (tableId: string, page = 1, size = 10) => {
    set({ isLoading: true, error: null });
    try {
      const response = await tableService.getImportHistory(tableId, page, size);
      set({ 
        importHistory: response.items,
        total: response.total,
        page: response.page,
        size: response.size,
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '获取导入历史失败' 
      });
    }
  },
  
  createPhysicalTable: async (id: string) => {
    try {
      return await tableService.createPhysicalTable(id);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '创建物理表失败');
    }
  },
  
  dropPhysicalTable: async (id: string) => {
    try {
      return await tableService.dropPhysicalTable(id);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '删除物理表失败');
    }
  },
  
  importData: async (tableId: string, file: File, config?: Record<string, any>) => {
    try {
      return await tableService.importData(tableId, file, config);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '导入数据失败');
    }
  },
}));

export default useTableStore;
