import { create } from 'zustand';
import { Project, ProjectCreate, ProjectUpdate } from '../types/api';
import projectService from '../services/projectService';

interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  isLoading: boolean;
  error: string | null;
  total: number;
  page: number;
  size: number;
  
  // Actions
  fetchProjects: (page?: number, size?: number) => Promise<void>;
  fetchProject: (id: string) => Promise<void>;
  createProject: (project: ProjectCreate) => Promise<void>;
  updateProject: (id: string, project: ProjectUpdate) => Promise<void>;
  deleteProject: (id: string) => Promise<void>;
  setCurrentProject: (project: Project | null) => void;
  testConnection: (id: string) => Promise<{ success: boolean; message: string }>;
}

const useProjectStore = create<ProjectState>((set, get) => ({
  projects: [],
  currentProject: null,
  isLoading: false,
  error: null,
  total: 0,
  page: 1,
  size: 10,
  
  fetchProjects: async (page = 1, size = 10) => {
    set({ isLoading: true, error: null });
    try {
      const response = await projectService.getProjects(page, size);
      set({ 
        projects: response.items,
        total: response.total,
        page: response.page,
        size: response.size,
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '获取项目列表失败' 
      });
    }
  },
  
  fetchProject: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      const project = await projectService.getProject(id);
      set({ currentProject: project, isLoading: false });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '获取项目详情失败' 
      });
    }
  },
  
  createProject: async (projectData: ProjectCreate) => {
    set({ isLoading: true, error: null });
    try {
      const project = await projectService.createProject(projectData);
      const { projects } = get();
      set({ 
        projects: [project, ...projects],
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '创建项目失败' 
      });
    }
  },
  
  updateProject: async (id: string, projectData: ProjectUpdate) => {
    set({ isLoading: true, error: null });
    try {
      const updatedProject = await projectService.updateProject(id, projectData);
      const { projects, currentProject } = get();
      
      set({ 
        projects: projects.map(p => p.id === id ? updatedProject : p),
        currentProject: currentProject?.id === id ? updatedProject : currentProject,
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '更新项目失败' 
      });
    }
  },
  
  deleteProject: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      await projectService.deleteProject(id);
      const { projects, currentProject } = get();
      
      set({ 
        projects: projects.filter(p => p.id !== id),
        currentProject: currentProject?.id === id ? null : currentProject,
        isLoading: false 
      });
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error instanceof Error ? error.message : '删除项目失败' 
      });
    }
  },
  
  setCurrentProject: (project: Project | null) => {
    set({ currentProject: project });
  },
  
  testConnection: async (id: string) => {
    try {
      return await projectService.testConnection(id);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : '测试连接失败');
    }
  },
}));

export default useProjectStore;
