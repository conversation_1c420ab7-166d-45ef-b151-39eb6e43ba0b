import api from './api';
import { LoginRequest, LoginResponse, RegisterRequest, User } from '../types/api';

// 认证服务
const authService = {
  // 登录
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    // 使用 JSON 格式发送请求
    const response = await api.post<LoginResponse>('/auth/login', {
      username: credentials.username,
      password: credentials.password
    });

    // 保存令牌到本地存储
    if (response.access_token) {
      localStorage.setItem('token', response.access_token);
      localStorage.setItem('user', JSON.stringify(response.user));
    }

    return response;
  },

  // 注册
  register: async (userData: RegisterRequest): Promise<User> => {
    return api.post<User>('/auth/register', userData);
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    return api.get<User>('/auth/me');
  },

  // 退出登录
  logout: (): void => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    window.location.href = '/login';
  },

  // 检查是否已登录
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem('token');
  },

  // 获取本地存储的用户信息
  getStoredUser: (): User | null => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr) as User;
      } catch (error) {
        console.error('Failed to parse user data', error);
        return null;
      }
    }
    return null;
  },
};

export default authService;
