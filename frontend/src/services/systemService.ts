import api from './api';

// 系统监控相关类型
export interface SystemInfo {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  database_status: 'connected' | 'disconnected';
  redis_status: 'connected' | 'disconnected';
  uptime: number;
}

export interface TaskStatus {
  id: string;
  name: string;
  type: string;
  status: 'pending' | 'running' | 'success' | 'failed';
  progress: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
}

// 系统监控服务
const systemService = {
  // 获取系统信息
  getSystemInfo: async (): Promise<SystemInfo> => {
    return api.get<SystemInfo>('/system/info');
  },
  
  // 获取健康检查状态
  getHealthCheck: async (): Promise<{ status: string; checks: Record<string, any> }> => {
    return api.get<{ status: string; checks: Record<string, any> }>('/system/health');
  },
  
  // 获取运行中的任务
  getRunningTasks: async (): Promise<TaskStatus[]> => {
    return api.get<TaskStatus[]>('/system/tasks');
  },
  
  // 获取任务历史
  getTaskHistory: async (
    page: number = 1, 
    size: number = 10
  ): Promise<{
    items: TaskStatus[];
    total: number;
    page: number;
    size: number;
  }> => {
    return api.get<{
      items: TaskStatus[];
      total: number;
      page: number;
      size: number;
    }>('/system/task-history', {
      params: { page, size },
    });
  },
  
  // 获取系统日志
  getLogs: async (
    level?: string,
    limit: number = 100
  ): Promise<{
    logs: Array<{
      timestamp: string;
      level: string;
      message: string;
      module?: string;
    }>;
  }> => {
    return api.get<{
      logs: Array<{
        timestamp: string;
        level: string;
        message: string;
        module?: string;
      }>;
    }>('/system/logs', {
      params: { level, limit },
    });
  },
  
  // 清理系统缓存
  clearCache: async (): Promise<{ success: boolean; message: string }> => {
    return api.post<{ success: boolean; message: string }>('/system/clear-cache');
  },
  
  // 重启服务
  restartService: async (service: string): Promise<{ success: boolean; message: string }> => {
    return api.post<{ success: boolean; message: string }>(`/system/restart/${service}`);
  },
};

export default systemService;
