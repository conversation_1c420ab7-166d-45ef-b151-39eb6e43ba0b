import api from './api';
import { 
  TrainingTask, 
  TrainingTaskCreate, 
  TrainingTaskUpdate,
  TrainingRecord,
  SqlTraining,
  SqlExample,
  SqlExampleCreate,
  SqlExampleUpdate
} from '../types/training';
import { PaginatedResponse } from '../types/api';

// 训练管理服务
const trainingService = {
  // 获取训练任务列表
  getTrainingTasks: async (
    projectId: string, 
    page: number = 1, 
    size: number = 10
  ): Promise<PaginatedResponse<TrainingTask>> => {
    return api.get<PaginatedResponse<TrainingTask>>('/training/tasks', {
      params: { project_id: projectId, page, size },
    });
  },
  
  // 获取单个训练任务
  getTrainingTask: async (id: string): Promise<TrainingTask> => {
    return api.get<TrainingTask>(`/training/tasks/${id}`);
  },
  
  // 创建训练任务
  createTrainingTask: async (
    projectId: string, 
    task: TrainingTaskCreate
  ): Promise<TrainingTask> => {
    return api.post<TrainingTask>('/training/tasks', { ...task, project_id: projectId });
  },
  
  // 更新训练任务
  updateTrainingTask: async (id: string, task: TrainingTaskUpdate): Promise<TrainingTask> => {
    return api.put<TrainingTask>(`/training/tasks/${id}`, task);
  },
  
  // 删除训练任务
  deleteTrainingTask: async (id: string): Promise<void> => {
    return api.delete<void>(`/training/tasks/${id}`);
  },
  
  // 启动训练任务
  startTraining: async (id: string): Promise<{ success: boolean; message: string }> => {
    return api.post<{ success: boolean; message: string }>(`/training/tasks/${id}/start`);
  },
  
  // 停止训练任务
  stopTraining: async (id: string): Promise<{ success: boolean; message: string }> => {
    return api.post<{ success: boolean; message: string }>(`/training/tasks/${id}/stop`);
  },
  
  // 获取训练记录
  getTrainingRecords: async (
    taskId: string, 
    page: number = 1, 
    size: number = 10
  ): Promise<PaginatedResponse<TrainingRecord>> => {
    return api.get<PaginatedResponse<TrainingRecord>>(`/training/tasks/${taskId}/records`, {
      params: { page, size },
    });
  },
  
  // 获取 SQL 训练集列表
  getSqlTrainings: async (
    projectId: string, 
    page: number = 1, 
    size: number = 10
  ): Promise<PaginatedResponse<SqlTraining>> => {
    return api.get<PaginatedResponse<SqlTraining>>('/training/sql-trainings', {
      params: { project_id: projectId, page, size },
    });
  },
  
  // 创建 SQL 训练集
  createSqlTraining: async (
    projectId: string, 
    training: { name: string; description?: string }
  ): Promise<SqlTraining> => {
    return api.post<SqlTraining>('/training/sql-trainings', { 
      ...training, 
      project_id: projectId 
    });
  },
  
  // 获取 SQL 示例列表
  getSqlExamples: async (
    trainingId: string, 
    page: number = 1, 
    size: number = 10
  ): Promise<PaginatedResponse<SqlExample>> => {
    return api.get<PaginatedResponse<SqlExample>>(`/training/sql-trainings/${trainingId}/examples`, {
      params: { page, size },
    });
  },
  
  // 创建 SQL 示例
  createSqlExample: async (
    trainingId: string, 
    example: SqlExampleCreate
  ): Promise<SqlExample> => {
    return api.post<SqlExample>(`/training/sql-trainings/${trainingId}/examples`, example);
  },
  
  // 更新 SQL 示例
  updateSqlExample: async (id: string, example: SqlExampleUpdate): Promise<SqlExample> => {
    return api.put<SqlExample>(`/training/sql-examples/${id}`, example);
  },
  
  // 删除 SQL 示例
  deleteSqlExample: async (id: string): Promise<void> => {
    return api.delete<void>(`/training/sql-examples/${id}`);
  },
  
  // 批量导入 SQL 示例
  importSqlExamples: async (
    trainingId: string, 
    file: File
  ): Promise<{ success: number; failed: number; message: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    
    return api.post<{ success: number; failed: number; message: string }>(
      `/training/sql-trainings/${trainingId}/import-examples`, 
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
  },
};

export default trainingService;
