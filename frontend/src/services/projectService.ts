import api from './api';
import { Project, ProjectCreate, ProjectUpdate, PaginatedResponse } from '../types/api';

// 项目服务
const projectService = {
  // 获取项目列表
  getProjects: async (page: number = 1, size: number = 10): Promise<PaginatedResponse<Project>> => {
    return api.get<PaginatedResponse<Project>>('/projects', {
      params: { page, size },
    });
  },
  
  // 获取单个项目
  getProject: async (id: string): Promise<Project> => {
    return api.get<Project>(`/projects/${id}`);
  },
  
  // 创建项目
  createProject: async (project: ProjectCreate): Promise<Project> => {
    return api.post<Project>('/projects', project);
  },
  
  // 更新项目
  updateProject: async (id: string, project: ProjectUpdate): Promise<Project> => {
    return api.put<Project>(`/projects/${id}`, project);
  },
  
  // 删除项目
  deleteProject: async (id: string): Promise<void> => {
    return api.delete<void>(`/projects/${id}`);
  },
  
  // 测试数据库连接
  testConnection: async (id: string): Promise<{ success: boolean; message: string }> => {
    return api.post<{ success: boolean; message: string }>(`/projects/${id}/test-connection`);
  },
  
  // 获取项目统计信息
  getProjectStats: async (id: string): Promise<{
    tables_count: number;
    active_tables: number;
    total_records: number;
    training_tasks: number;
  }> => {
    return api.get<{
      tables_count: number;
      active_tables: number;
      total_records: number;
      training_tasks: number;
    }>(`/projects/${id}/stats`);
  },
};

export default projectService;
