import api from './api';
import { 
  TableSchema, 
  TableCreate, 
  TableUpdate, 
  ColumnSchema,
  DataImport,
  DataImportCreate
} from '../types/table';
import { PaginatedResponse } from '../types/api';

// 表管理服务
const tableService = {
  // 获取表列表
  getTables: async (
    projectId: string, 
    page: number = 1, 
    size: number = 10
  ): Promise<PaginatedResponse<TableSchema>> => {
    return api.get<PaginatedResponse<TableSchema>>('/tables', {
      params: { project_id: projectId, page, size },
    });
  },
  
  // 获取单个表
  getTable: async (id: string): Promise<TableSchema> => {
    return api.get<TableSchema>(`/tables/${id}`);
  },
  
  // 创建表
  createTable: async (projectId: string, table: TableCreate): Promise<TableSchema> => {
    return api.post<TableSchema>('/tables', { ...table, project_id: projectId });
  },
  
  // 更新表
  updateTable: async (id: string, table: TableUpdate): Promise<TableSchema> => {
    return api.put<TableSchema>(`/tables/${id}`, table);
  },
  
  // 删除表
  deleteTable: async (id: string): Promise<void> => {
    return api.delete<void>(`/tables/${id}`);
  },
  
  // 获取表的列
  getColumns: async (tableId: string): Promise<ColumnSchema[]> => {
    return api.get<ColumnSchema[]>(`/tables/${tableId}/columns`);
  },
  
  // 创建物理表
  createPhysicalTable: async (id: string): Promise<{ success: boolean; message: string }> => {
    return api.post<{ success: boolean; message: string }>(`/tables/${id}/create-physical`);
  },
  
  // 删除物理表
  dropPhysicalTable: async (id: string): Promise<{ success: boolean; message: string }> => {
    return api.post<{ success: boolean; message: string }>(`/tables/${id}/drop-physical`);
  },
  
  // 获取表数据预览
  getTablePreview: async (id: string, limit: number = 10): Promise<any[]> => {
    return api.get<any[]>(`/tables/${id}/preview`, {
      params: { limit },
    });
  },
  
  // 获取表的导入历史
  getImportHistory: async (
    tableId: string, 
    page: number = 1, 
    size: number = 10
  ): Promise<PaginatedResponse<DataImport>> => {
    return api.get<PaginatedResponse<DataImport>>(`/tables/${tableId}/imports`, {
      params: { page, size },
    });
  },
  
  // 上传文件并导入数据
  importData: async (
    tableId: string, 
    file: File, 
    config?: Record<string, any>
  ): Promise<DataImport> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('table_id', tableId);
    
    if (config) {
      formData.append('import_config', JSON.stringify(config));
    }
    
    return api.post<DataImport>('/data-import/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // 获取导入任务状态
  getImportStatus: async (importId: string): Promise<DataImport> => {
    return api.get<DataImport>(`/data-import/${importId}`);
  },
  
  // 取消导入任务
  cancelImport: async (importId: string): Promise<{ success: boolean; message: string }> => {
    return api.post<{ success: boolean; message: string }>(`/data-import/${importId}/cancel`);
  },
};

export default tableService;
