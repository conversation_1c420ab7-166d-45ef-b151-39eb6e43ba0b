import React, { useState } from 'react';
import { Form, Input, But<PERSON>, Card, Typography, Al<PERSON>, Spin } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import useAuthStore from '../stores/authStore';
import { LoginRequest } from '../types/api';

const { Title } = Typography;

interface LocationState {
  from?: {
    pathname: string;
  };
}

const Login: React.FC = () => {
  const { login, isLoading, error } = useAuthStore();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const [loginError, setLoginError] = useState<string | null>(null);

  // 获取用户想要访问的页面
  const from = (location.state as LocationState)?.from?.pathname || '/';

  const handleSubmit = async (values: LoginRequest) => {
    setLoginError(null);
    
    try {
      await login(values);
      // 登录成功后重定向到用户想要访问的页面
      navigate(from, { replace: true });
    } catch (err) {
      setLoginError(err instanceof Error ? err.message : '登录失败，请检查用户名和密码');
    }
  };

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh',
      background: '#f0f2f5'
    }}>
      <Card style={{ width: 400, boxShadow: '0 4px 12px rgba(0,0,0,0.1)' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={2}>Vanna Manager</Title>
          <Title level={4} style={{ fontWeight: 'normal', marginTop: 0 }}>
            登录系统
          </Title>
        </div>
        
        {(error || loginError) && (
          <Alert 
            message="登录错误" 
            description={error || loginError} 
            type="error" 
            showIcon 
            style={{ marginBottom: 24 }}
            closable
          />
        )}
        
        <Spin spinning={isLoading}>
          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            autoComplete="off"
            layout="vertical"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input 
                prefix={<UserOutlined />} 
                placeholder="用户名" 
                size="large" 
              />
            </Form.Item>
            
            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password 
                prefix={<LockOutlined />} 
                placeholder="密码" 
                size="large" 
              />
            </Form.Item>
            
            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                size="large" 
                block
                loading={isLoading}
              >
                登录
              </Button>
            </Form.Item>
          </Form>
        </Spin>
      </Card>
    </div>
  );
};

export default Login;
