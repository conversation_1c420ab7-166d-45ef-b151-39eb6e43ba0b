import React, { useEffect, useState } from 'react'
import { Card, Row, Col, Statistic, Progress, List, Tag, Spin, Alert } from 'antd'
import {
  TableOutlined,
  DatabaseOutlined,
  RobotOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons'
import useProjectStore from '../stores/projectStore'
import { TaskStatus } from '../services/systemService'
import systemService from '../services/systemService'

const Dashboard: React.FC = () => {
  const { currentProject } = useProjectStore()
  const [stats, setStats] = useState({
    totalTables: 0,
    activeTables: 0,
    totalRecords: 0,
    trainingTasks: 0,
  })
  const [recentTasks, setRecentTasks] = useState<TaskStatus[]>([])
  const [systemInfo, setSystemInfo] = useState({
    cpu_usage: 0,
    memory_usage: 0,
    disk_usage: 0,
    database_status: 'connected' as 'connected' | 'disconnected',
    redis_status: 'connected' as 'connected' | 'disconnected',
    uptime: 0,
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 获取项目统计信息
  useEffect(() => {
    const fetchProjectStats = async () => {
      if (!currentProject) return

      setLoading(true)
      setError(null)

      try {
        const projectStats = await useProjectStore.getState().fetchProject(currentProject.id)
        const stats = await useProjectStore.getState().testConnection(currentProject.id)

        setStats({
          totalTables: projectStats?.tables_count || 0,
          activeTables: projectStats?.active_tables || 0,
          totalRecords: projectStats?.total_records || 0,
          trainingTasks: projectStats?.training_tasks || 0,
        })
      } catch (err) {
        setError('获取项目统计信息失败')
        console.error(err)
      } finally {
        setLoading(false)
      }
    }

    fetchProjectStats()
  }, [currentProject])

  // 获取系统信息和任务状态
  useEffect(() => {
    const fetchSystemInfo = async () => {
      setLoading(true)

      try {
        const [sysInfo, tasks] = await Promise.all([
          systemService.getSystemInfo(),
          systemService.getRunningTasks(),
        ])

        setSystemInfo(sysInfo)
        setRecentTasks(tasks.slice(0, 3))
      } catch (err) {
        console.error(err)
      } finally {
        setLoading(false)
      }
    }

    fetchSystemInfo()

    // 每30秒刷新一次系统信息
    const intervalId = setInterval(fetchSystemInfo, 30000)
    return () => clearInterval(intervalId)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'green'
      case 'running':
        return 'blue'
      case 'pending':
        return 'orange'
      case 'failed':
        return 'red'
      default:
        return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'success':
        return '已完成'
      case 'running':
        return '进行中'
      case 'pending':
        return '等待中'
      case 'failed':
        return '失败'
      default:
        return '未知'
    }
  }

  // 格式化时间
  const formatTime = (timeStr: string) => {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
  };

  return (
    <div className="page-container">
      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
          closable
        />
      )}

      <Spin spinning={loading}>
        <Row gutter={[16, 16]}>
          {/* 统计卡片 */}
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总表数"
                value={stats.totalTables}
                prefix={<TableOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="活跃表数"
                value={stats.activeTables}
                prefix={<DatabaseOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总记录数"
                value={stats.totalRecords}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="训练任务"
                value={stats.trainingTasks}
                prefix={<RobotOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          {/* 最近任务 */}
          <Col xs={24} lg={12}>
            <Card title="最近任务" size="small">
              {recentTasks.length > 0 ? (
                <List
                  dataSource={recentTasks}
                  renderItem={(item) => (
                    <List.Item>
                      <List.Item.Meta
                        title={
                          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <span>{item.name}</span>
                            <Tag color={getStatusColor(item.status)}>
                              {getStatusText(item.status)}
                            </Tag>
                          </div>
                        }
                        description={
                          <div>
                            <Progress percent={item.progress} size="small" />
                            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                              {formatTime(item.created_at)}
                            </div>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              ) : (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  暂无任务
                </div>
              )}
            </Card>
          </Col>

          {/* 系统状态 */}
          <Col xs={24} lg={12}>
            <Card title="系统状态" size="small">
              <div style={{ marginBottom: 16 }}>
                <div style={{ marginBottom: 8 }}>CPU 使用率</div>
                <Progress percent={Math.round(systemInfo.cpu_usage * 100)} status="active" />
              </div>
              <div style={{ marginBottom: 16 }}>
                <div style={{ marginBottom: 8 }}>内存使用率</div>
                <Progress percent={Math.round(systemInfo.memory_usage * 100)} status="active" />
              </div>
              <div style={{ marginBottom: 16 }}>
                <div style={{ marginBottom: 8 }}>磁盘使用率</div>
                <Progress percent={Math.round(systemInfo.disk_usage * 100)} />
              </div>
              <div>
                <div style={{ marginBottom: 8 }}>数据库连接</div>
                <Progress
                  percent={systemInfo.database_status === 'connected' ? 100 : 0}
                  status={systemInfo.database_status === 'connected' ? 'success' : 'exception'}
                />
              </div>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  )
}

export default Dashboard
