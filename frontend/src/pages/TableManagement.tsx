import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Spin,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  StopOutlined,
  EyeOutlined,
  UploadOutlined,
  DownloadOutlined,
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import useProjectStore from '../stores/projectStore'
import useTableStore from '../stores/tableStore'
import { TableSchema, ColumnSchema, TableCreate, TableUpdate, DATA_TYPES } from '../types/table'

const { TextArea } = Input
const { Option } = Select

const TableManagement: React.FC = () => {
  const { currentProject } = useProjectStore()
  const {
    tables,
    currentTable,
    isLoading,
    error,
    fetchTables,
    createTable,
    updateTable,
    deleteTable,
    setCurrentTable,
    createPhysicalTable,
    dropPhysicalTable,
  } = useTableStore()

  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [viewModalVisible, setViewModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [editForm] = Form.useForm()

  // 加载表列表
  useEffect(() => {
    if (currentProject) {
      fetchTables(currentProject.id)
    }
  }, [currentProject, fetchTables])

  // 显示错误信息
  useEffect(() => {
    if (error) {
      message.error(error)
    }
  }, [error])

  // 表格列定义
  const columns: ColumnsType<TableSchema> = [
    {
      title: '表名',
      dataIndex: 'table_name',
      key: 'table_name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {record.display_name}
          </div>
        </div>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '状态',
      key: 'status',
      render: (_, record) => (
        <Space>
          <Tag color={record.is_created ? 'green' : 'orange'}>
            {record.is_created ? '已创建' : '未创建'}
          </Tag>
          <Tag color={record.is_active ? 'blue' : 'red'}>
            {record.is_active ? '活跃' : '禁用'}
          </Tag>
        </Space>
      ),
    },
    {
      title: '数据行数',
      dataIndex: 'row_count',
      key: 'row_count',
      render: (count) => count.toLocaleString(),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          {!record.is_created ? (
            <Tooltip title="创建物理表">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => handleCreatePhysical(record)}
              />
            </Tooltip>
          ) : (
            <Tooltip title="删除物理表">
              <Popconfirm
                title="确定要删除物理表吗？"
                onConfirm={() => handleDropPhysical(record)}
              >
                <Button
                  type="text"
                  icon={<StopOutlined />}
                  danger
                />
              </Popconfirm>
            </Tooltip>
          )}
          <Popconfirm
            title="确定要删除这个表吗？"
            onConfirm={() => handleDelete(record)}
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              danger
            />
          </Popconfirm>
        </Space>
      ),
    },
  ]

  // 处理创建表
  const handleCreate = () => {
    setCreateModalVisible(true)
    form.resetFields()
  }

  // 处理编辑表
  const handleEdit = (table: TableSchema) => {
    setCurrentTable(table)
    setEditModalVisible(true)
    editForm.setFieldsValue({
      display_name: table.display_name,
      description: table.description,
    })
  }

  // 处理查看表
  const handleView = (table: TableSchema) => {
    setCurrentTable(table)
    setViewModalVisible(true)
  }

  // 处理删除表
  const handleDelete = async (table: TableSchema) => {
    try {
      await deleteTable(id)
      message.success('表删除成功')
    } catch (error) {
      message.error('删除表失败: ' + (error instanceof Error ? error.message : String(error)))
    }
  }

  // 处理创建物理表
  const handleCreatePhysical = async (table: TableSchema) => {
    try {
      const result = await createPhysicalTable(table.id)
      if (result.success) {
        message.success(result.message || '物理表创建成功')
        if (currentProject) {
          fetchTables(currentProject.id)
        }
      } else {
        message.error(result.message || '物理表创建失败')
      }
    } catch (error) {
      message.error('创建物理表失败: ' + (error instanceof Error ? error.message : String(error)))
    }
  }

  // 处理删除物理表
  const handleDropPhysical = async (table: TableSchema) => {
    try {
      const result = await dropPhysicalTable(table.id)
      if (result.success) {
        message.success(result.message || '物理表删除成功')
        if (currentProject) {
          fetchTables(currentProject.id)
        }
      } else {
        message.error(result.message || '物理表删除失败')
      }
    } catch (error) {
      message.error('删除物理表失败: ' + (error instanceof Error ? error.message : String(error)))
    }
  }

  // 提交创建表单
  const handleCreateSubmit = async (values: any) => {
    if (!currentProject) {
      message.error('请先选择项目')
      return
    }

    try {
      const tableData: TableCreate = {
        table_name: values.table_name,
        display_name: values.display_name,
        description: values.description,
        columns: values.columns || [],
      }

      await createTable(currentProject.id, tableData)
      message.success('表创建成功')
      setCreateModalVisible(false)
      form.resetFields()
    } catch (error) {
      message.error('创建表失败: ' + (error instanceof Error ? error.message : String(error)))
    }
  }

  // 提交编辑表单
  const handleEditSubmit = async (values: any) => {
    if (!currentTable) return

    try {
      const tableData: TableUpdate = {
        display_name: values.display_name,
        description: values.description,
        is_active: values.is_active,
      }

      await updateTable(currentTable.id, tableData)
      message.success('表更新成功')
      setEditModalVisible(false)
      editForm.resetFields()
    } catch (error) {
      message.error('更新表失败: ' + (error instanceof Error ? error.message : String(error)))
    }
  }

  return (
    <div className="page-container">
      <Card
        title="表管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            创建表
          </Button>
        }
      >
        <Spin spinning={isLoading}>
          <Table
            columns={columns}
            dataSource={tables}
            rowKey="id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
          />
        </Spin>
      </Card>

      {/* 创建表模态框 */}
      <Modal
        title="创建表"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateSubmit}
        >
          <Form.Item
            name="table_name"
            label="表名"
            rules={[{ required: true, message: '请输入表名' }]}
          >
            <Input placeholder="请输入表名（英文）" />
          </Form.Item>
          <Form.Item
            name="display_name"
            label="显示名称"
          >
            <Input placeholder="请输入显示名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={3} placeholder="请输入表描述" />
          </Form.Item>
          {/* TODO: 添加列定义表单 */}
        </Form>
      </Modal>

      {/* 编辑表模态框 */}
      <Modal
        title="编辑表"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={() => editForm.submit()}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditSubmit}
        >
          <Form.Item
            name="display_name"
            label="显示名称"
          >
            <Input placeholder="请输入显示名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={3} placeholder="请输入表描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看表详情模态框 */}
      <Modal
        title="表详情"
        open={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={null}
        width={800}
      >
        {currentTable && (
          <div>
            <p><strong>表名：</strong>{currentTable.table_name}</p>
            <p><strong>显示名称：</strong>{currentTable.display_name}</p>
            <p><strong>描述：</strong>{currentTable.description}</p>
            <p><strong>状态：</strong>
              <Tag color={currentTable.is_created ? 'green' : 'orange'}>
                {currentTable.is_created ? '已创建' : '未创建'}
              </Tag>
            </p>
            <p><strong>数据行数：</strong>{currentTable.row_count.toLocaleString()}</p>
            {/* TODO: 显示列信息 */}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default TableManagement
