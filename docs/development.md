# 开发指南

## 环境要求

### 系统要求
- Python 3.9+
- Node.js 18+
- Git

### 推荐开发工具
- VS Code / PyCharm
- Postman / Insomnia (API测试)
- DBeaver / pgAdmin (数据库管理)

## 项目结构

```
vanna-admin/
├── backend/                 # FastAPI后端
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── schemas/        # Pydantic模型
│   │   ├── services/       # 业务逻辑
│   │   └── main.py         # 应用入口
│   ├── requirements.txt    # Python依赖
│   ├── run.py             # 启动脚本
│   └── .env               # 环境配置
├── frontend/              # React前端 (待开发)
├── docs/                  # 项目文档
└── README.md
```

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd vanna-admin
```

### 2. 后端开发环境搭建

#### 创建虚拟环境
```bash
cd backend
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

#### 安装依赖
```bash
pip install -r requirements.txt
```

#### 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等
```

#### 启动后端服务
```bash
python run.py
```

服务启动后访问：
- API服务: http://localhost:8000
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

### 3. 数据库初始化

项目使用SQLite作为默认数据库，首次启动会自动创建表结构。

如需使用PostgreSQL：
```bash
# 安装PostgreSQL依赖
pip install psycopg2-binary

# 修改.env文件
DATABASE_URL=postgresql://username:password@localhost:5432/vanna_admin
```

## 开发工作流

### 代码规范
- 使用Black进行代码格式化
- 遵循PEP 8规范
- 使用类型注解
- 编写单元测试

### Git工作流
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交代码
git add .
git commit -m "feat: add new feature"

# 推送分支
git push origin feature/new-feature
```

### 测试
```bash
# 运行测试
pytest

# 测试覆盖率
pytest --cov=app
```