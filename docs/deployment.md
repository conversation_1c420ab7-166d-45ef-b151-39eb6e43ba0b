# 部署指南

## Docker部署 (推荐)

### 1. 创建Dockerfile

```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "run.py"]
```

### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/vanna_admin
    depends_on:
      - db
      - redis
    volumes:
      - ./backend:/app
    
  db:
    image: postgres:14
    environment:
      POSTGRES_DB: vanna_admin
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

### 3. 启动服务

```bash
docker-compose up -d
```

## 手动部署

### 1. 系统要求
- Ubuntu 20.04+ / CentOS 8+
- Python 3.9+
- PostgreSQL 14+
- Redis 6+
- Nginx (可选)

### 2. 安装依赖

```bash
# Ubuntu
sudo apt update
sudo apt install python3 python3-pip python3-venv postgresql postgresql-contrib redis-server

# CentOS
sudo yum install python3 python3-pip postgresql-server postgresql-contrib redis
```

### 3. 配置数据库

```bash
# 启动PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库
sudo -u postgres psql
CREATE DATABASE vanna_admin;
CREATE USER vanna_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE vanna_admin TO vanna_user;
\q
```

### 4. 部署应用

```bash
# 克隆代码
git clone <repository-url>
cd vanna-admin/backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件

# 启动应用
python run.py
```

### 5. 配置Nginx (可选)

```nginx
# /etc/nginx/sites-available/vanna-admin
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 6. 配置系统服务

```ini
# /etc/systemd/system/vanna-admin.service
[Unit]
Description=Vanna Admin API
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/vanna-admin/backend
Environment=PATH=/path/to/vanna-admin/backend/venv/bin
ExecStart=/path/to/vanna-admin/backend/venv/bin/python run.py
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
sudo systemctl daemon-reload
sudo systemctl enable vanna-admin
sudo systemctl start vanna-admin
```

## 监控和日志

### 日志配置
```python
# backend/app/core/logging.py
import logging
import sys

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
```

### 健康检查
```bash
curl http://localhost:8000/health
```

## 性能优化

### 1. 数据库优化
- 添加适当的索引
- 配置连接池
- 定期清理日志

### 2. 应用优化
- 使用Gunicorn作为WSGI服务器
- 配置Redis缓存
- 启用gzip压缩

### 3. Gunicorn配置
```bash
pip install gunicorn

# 启动命令
gunicorn -w 4 -k uvicorn.workers.UvicornWorker app.main:app --bind 0.0.0.0:8000
```