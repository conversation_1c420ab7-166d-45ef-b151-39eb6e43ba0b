"""
性能测试文件
使用Locust进行负载测试
"""
from locust import HttpUser, task, between
import json
import random


class VannaManagerUser(HttpUser):
    """Vanna Manager用户行为模拟"""

    wait_time = between(1, 3)  # 用户操作间隔1-3秒

    def on_start(self):
        """用户开始时的初始化操作"""
        self.login()

    def login(self):
        """用户登录"""
        login_data = {
            "username": "testuser",
            "password": "testpassword123"
        }

        # 先注册用户（如果不存在）
        register_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "testpassword123",
            "full_name": "Test User"
        }

        with self.client.post("/api/v1/auth/register", json=register_data, catch_response=True) as response:
            if response.status_code in [200, 400]:  # 200成功，400可能是用户已存在
                pass
            else:
                response.failure(f"注册失败: {response.status_code}")

        # 登录获取token
        with self.client.post("/api/v1/auth/login", json=login_data, catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                self.headers = {"Authorization": f"Bearer {self.token}"}
                response.success()
            else:
                response.failure(f"登录失败: {response.status_code}")
                self.headers = {}

    @task(3)
    def get_projects(self):
        """获取项目列表"""
        with self.client.get("/api/v1/projects/", headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取项目列表失败: {response.status_code}")

    @task(2)
    def get_tables(self):
        """获取表列表"""
        project_id = "test-project-id"  # 假设的项目ID
        with self.client.get(f"/api/v1/tables/?project_id={project_id}", headers=self.headers, catch_response=True) as response:
            if response.status_code in [200, 404]:  # 404可能是项目不存在
                response.success()
            else:
                response.failure(f"获取表列表失败: {response.status_code}")

    @task(1)
    def create_project(self):
        """创建项目"""
        project_data = {
            "name": f"Test Project {random.randint(1000, 9999)}",
            "description": "Performance test project",
            "database_type": "postgresql",
            "database_host": "localhost",
            "database_port": "5432",
            "database_name": "test_db",
            "database_user": "test_user",
            "database_password": "test_password"
        }

        with self.client.post("/api/v1/projects/", json=project_data, headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            elif response.status_code == 400:  # 可能是重复名称
                response.success()
            else:
                response.failure(f"创建项目失败: {response.status_code}")

    @task(2)
    def get_system_health(self):
        """检查系统健康状态"""
        with self.client.get("/api/v1/system/health", catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"健康检查失败: {response.status_code}")

    @task(1)
    def get_system_metrics(self):
        """获取系统指标"""
        with self.client.get("/api/v1/system/metrics", headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取系统指标失败: {response.status_code}")

    @task(1)
    def get_training_tasks(self):
        """获取训练任务列表"""
        with self.client.get("/api/v1/training/", headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取训练任务失败: {response.status_code}")

    @task(1)
    def get_import_tasks(self):
        """获取导入任务列表"""
        with self.client.get("/api/v1/data-import/", headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取导入任务失败: {response.status_code}")


class AdminUser(HttpUser):
    """管理员用户行为模拟"""

    wait_time = between(2, 5)
    weight = 1  # 管理员用户权重较低

    def on_start(self):
        """管理员登录"""
        self.login_as_admin()

    def login_as_admin(self):
        """管理员登录"""
        # 这里应该使用管理员账户
        login_data = {
            "username": "admin",
            "password": "adminpassword123"
        }

        with self.client.post("/api/v1/auth/login", json=login_data, catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                self.headers = {"Authorization": f"Bearer {self.token}"}
                response.success()
            else:
                response.failure(f"管理员登录失败: {response.status_code}")
                self.headers = {}

    @task(2)
    def get_active_tasks(self):
        """获取活跃任务"""
        with self.client.get("/api/v1/tasks/active", headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取活跃任务失败: {response.status_code}")

    @task(1)
    def get_task_stats(self):
        """获取任务统计"""
        with self.client.get("/api/v1/tasks/stats", headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"获取任务统计失败: {response.status_code}")

    @task(1)
    def trigger_cleanup(self):
        """触发清理任务"""
        with self.client.post("/api/v1/tasks/cleanup/temp-files", headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"触发清理任务失败: {response.status_code}")


class APIOnlyUser(HttpUser):
    """纯API用户（模拟第三方集成）"""

    wait_time = between(0.5, 2)
    weight = 2

    def on_start(self):
        """API用户初始化"""
        self.get_api_token()

    def get_api_token(self):
        """获取API令牌"""
        # 模拟API密钥认证
        self.headers = {"X-API-Key": "test-api-key"}

    @task(5)
    def api_health_check(self):
        """API健康检查"""
        with self.client.get("/api/v1/system/health", headers=self.headers, catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"API健康检查失败: {response.status_code}")

    @task(3)
    def api_get_projects(self):
        """API获取项目"""
        with self.client.get("/api/v1/projects/", headers=self.headers, catch_response=True) as response:
            if response.status_code in [200, 401]:  # 401可能是API密钥无效
                response.success()
            else:
                response.failure(f"API获取项目失败: {response.status_code}")

    @task(2)
    def api_get_metrics(self):
        """API获取指标"""
        with self.client.get("/api/v1/system/metrics", headers=self.headers, catch_response=True) as response:
            if response.status_code in [200, 401]:
                response.success()
            else:
                response.failure(f"API获取指标失败: {response.status_code}")
