#!/usr/bin/env python3
"""
创建管理员用户脚本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.core.security import get_password_hash
from app.models.user import User

def create_admin_user():
    """创建管理员用户"""
    db: Session = SessionLocal()
    
    try:
        # 检查是否已存在管理员用户
        existing_user = db.query(User).filter(
            (User.username == "admin") | (User.email == "<EMAIL>")
        ).first()
        
        if existing_user:
            print("管理员用户已存在:")
            print(f"  用户名: {existing_user.username}")
            print(f"  邮箱: {existing_user.email}")
            print(f"  全名: {existing_user.full_name}")
            return
        
        # 创建管理员用户
        hashed_password = get_password_hash("admin123")
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=hashed_password,
            full_name="系统管理员",
            is_active=True,
            is_superuser=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print("管理员用户创建成功:")
        print(f"  用户名: {admin_user.username}")
        print(f"  邮箱: {admin_user.email}")
        print(f"  密码: admin123")
        print(f"  全名: {admin_user.full_name}")
        print(f"  用户ID: {admin_user.id}")
        
    except Exception as e:
        print(f"创建管理员用户失败: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
