"""
依赖项
"""
from typing import Optional
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from .database import get_db
from .security import get_user_from_token, check_permissions, rate_limit_check, log_security_event
from app.models.user import User

# HTTP Bearer认证
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前用户"""
    try:
        user = get_user_from_token(credentials.credentials, db)
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用"
        )
    return current_user


async def get_current_superuser(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """获取当前超级用户"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    return current_user


def require_permissions(permissions: list):
    """权限检查装饰器"""
    def permission_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if not check_permissions(current_user, permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )
        return current_user

    return permission_checker


async def rate_limiter(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """速率限制中间件"""
    endpoint = request.url.path
    user_id = str(current_user.id)

    # 检查速率限制
    if not rate_limit_check(user_id, endpoint):
        # 记录安全事件
        log_security_event(
            "rate_limit_exceeded",
            user_id,
            {"endpoint": endpoint, "ip": request.client.host},
            db
        )

        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="请求过于频繁，请稍后再试"
        )


class PermissionChecker:
    """权限检查类"""

    def __init__(self, required_permissions: list):
        self.required_permissions = required_permissions

    def __call__(self, current_user: User = Depends(get_current_active_user)) -> User:
        if not check_permissions(current_user, self.required_permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足"
            )
        return current_user


# 预定义的权限检查器
require_admin = PermissionChecker(["admin"])
require_user_management = PermissionChecker(["user_management"])
require_project_management = PermissionChecker(["project_management"])
require_table_management = PermissionChecker(["table_management"])
require_data_import = PermissionChecker(["data_import"])
require_training_management = PermissionChecker(["training_management"])
require_system_monitoring = PermissionChecker(["system_monitoring"])


async def optional_auth(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """可选认证（允许匿名访问）"""
    if credentials is None:
        return None

    try:
        user = get_user_from_token(credentials.credentials, db)
        return user
    except HTTPException:
        return None


async def validate_request_data(request: Request):
    """验证请求数据"""
    # 检查请求大小
    content_length = request.headers.get('content-length')
    if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail="请求数据过大"
        )

    # 检查Content-Type
    content_type = request.headers.get('content-type', '')
    allowed_types = [
        'application/json',
        'application/x-www-form-urlencoded',
        'multipart/form-data'
    ]

    if not any(allowed_type in content_type for allowed_type in allowed_types):
        if content_type:  # 如果有Content-Type但不在允许列表中
            raise HTTPException(
                status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                detail="不支持的媒体类型"
            )


async def security_headers_middleware(request: Request, call_next):
    """安全头中间件"""
    response = await call_next(request)

    # 添加安全头
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

    # 为开发环境放宽 CSP 策略，允许 Swagger UI 资源
    if request.url.path in ["/docs", "/redoc"]:
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
            "img-src 'self' data: https://fastapi.tiangolo.com; "
            "font-src 'self' https://cdn.jsdelivr.net"
        )
    else:
        response.headers["Content-Security-Policy"] = "default-src 'self'"

    return response


def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    # 检查代理头
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    return request.client.host if request.client else "unknown"


async def log_request(
    request: Request,
    current_user: Optional[User] = Depends(optional_auth)
):
    """请求日志记录"""
    import structlog
    logger = structlog.get_logger()

    logger.info(
        "API request",
        method=request.method,
        url=str(request.url),
        user_id=str(current_user.id) if current_user else None,
        client_ip=get_client_ip(request),
        user_agent=request.headers.get("User-Agent", ""),
        timestamp=datetime.utcnow().isoformat()
    )


from datetime import datetime  # 添加缺失的导入
