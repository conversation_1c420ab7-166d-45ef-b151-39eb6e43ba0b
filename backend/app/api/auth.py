"""
认证API
"""
from datetime import timed<PERSON><PERSON>
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from pydantic import BaseModel, EmailStr, Field, field_validator
from uuid import UUID

from app.core.database import get_db
from app.core.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    create_refresh_token,
    verify_token,
    log_security_event
)
from app.core.deps import get_current_user, get_current_active_user, get_client_ip
from app.models.user import User

router = APIRouter()


# Pydantic模型定义
class UserLogin(BaseModel):
    """用户登录请求模型"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")


class UserRegister(BaseModel):
    """用户注册请求模型"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱")
    password: str = Field(..., min_length=6, description="密码")
    full_name: Optional[str] = Field(None, description="全名")


class UserResponse(BaseModel):
    """用户响应模型"""
    id: str
    username: str
    email: str
    full_name: Optional[str]
    is_active: bool
    is_superuser: bool
    created_at: str
    last_login: Optional[str]

    @field_validator('id', mode='before')
    @classmethod
    def validate_id(cls, v):
        """将UUID转换为字符串"""
        if isinstance(v, UUID):
            return str(v)
        return v

    @field_validator('created_at', mode='before')
    @classmethod
    def validate_created_at(cls, v):
        """将datetime转换为字符串"""
        if hasattr(v, 'isoformat'):
            return v.isoformat()
        return v

    @field_validator('last_login', mode='before')
    @classmethod
    def validate_last_login(cls, v):
        """将datetime转换为字符串"""
        if v is None:
            return None
        if hasattr(v, 'isoformat'):
            return v.isoformat()
        return v

    class Config:
        from_attributes = True


class TokenResponse(BaseModel):
    """令牌响应模型"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class RefreshTokenRequest(BaseModel):
    """刷新令牌请求模型"""
    refresh_token: str


class ChangePasswordRequest(BaseModel):
    """修改密码请求模型"""
    old_password: str
    new_password: str = Field(..., min_length=6)


@router.post("/register", response_model=UserResponse)
async def register(
    user_data: UserRegister,
    request: Request,
    db: Session = Depends(get_db)
):
    """用户注册"""
    try:
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(
            (User.username == user_data.username) | (User.email == user_data.email)
        ).first()

        if existing_user:
            if existing_user.username == user_data.username:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户名已存在"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已存在"
                )

        # 创建新用户
        hashed_password = get_password_hash(user_data.password)
        user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hashed_password,
            full_name=user_data.full_name
        )

        db.add(user)
        db.commit()
        db.refresh(user)

        # 记录安全事件
        log_security_event(
            "user_registered",
            str(user.id),
            {
                "username": user.username,
                "email": user.email,
                "ip": get_client_ip(request)
            },
            db
        )

        return user

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"注册失败: {str(e)}"
        )


@router.post("/login", response_model=TokenResponse)
async def login(
    user_data: UserLogin,
    request: Request,
    db: Session = Depends(get_db)
):
    """用户登录"""
    try:
        # 查找用户（支持用户名或邮箱登录）
        user = db.query(User).filter(
            (User.username == user_data.username) | (User.email == user_data.username)
        ).first()

        if not user or not verify_password(user_data.password, user.hashed_password):
            # 记录登录失败事件
            log_security_event(
                "login_failed",
                user_data.username,
                {"ip": get_client_ip(request)},
                db
            )

            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )

        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户已被禁用"
            )

        # 创建访问令牌和刷新令牌
        access_token_expires = timedelta(minutes=30)  # 30分钟
        access_token = create_access_token(
            data={"sub": str(user.id)},
            expires_delta=access_token_expires
        )

        refresh_token = create_refresh_token(data={"sub": str(user.id)})

        # 更新最后登录时间
        from datetime import datetime
        user.last_login = datetime.utcnow()
        db.commit()

        # 记录登录成功事件
        log_security_event(
            "login_success",
            str(user.id),
            {
                "username": user.username,
                "ip": get_client_ip(request)
            },
            db
        )

        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=1800,  # 30分钟
            user=user
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    token_data: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """刷新访问令牌"""
    try:
        # 验证刷新令牌
        payload = verify_token(token_data.refresh_token)

        if payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )

        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )

        # 获取用户信息
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在或已被禁用"
            )

        # 创建新的访问令牌
        access_token_expires = timedelta(minutes=30)
        access_token = create_access_token(
            data={"sub": str(user.id)},
            expires_delta=access_token_expires
        )

        # 创建新的刷新令牌
        new_refresh_token = create_refresh_token(data={"sub": str(user.id)})

        return TokenResponse(
            access_token=access_token,
            refresh_token=new_refresh_token,
            expires_in=1800,
            user=user
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"刷新令牌失败: {str(e)}"
        )


@router.post("/logout")
async def logout(
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """用户登出"""
    try:
        # 记录登出事件
        log_security_event(
            "logout",
            str(current_user.id),
            {
                "username": current_user.username,
                "ip": get_client_ip(request)
            },
            db
        )

        # TODO: 在实际应用中，可以将令牌加入黑名单

        return {"message": "登出成功"}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登出失败: {str(e)}"
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """获取当前用户信息"""
    return current_user


@router.put("/change-password")
async def change_password(
    password_data: ChangePasswordRequest,
    request: Request,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """修改密码"""
    try:
        # 验证旧密码
        if not verify_password(password_data.old_password, current_user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="旧密码错误"
            )

        # 更新密码
        current_user.hashed_password = get_password_hash(password_data.new_password)
        db.commit()

        # 记录密码修改事件
        log_security_event(
            "password_changed",
            str(current_user.id),
            {
                "username": current_user.username,
                "ip": get_client_ip(request)
            },
            db
        )

        return {"message": "密码修改成功"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"修改密码失败: {str(e)}"
        )


@router.post("/verify-token")
async def verify_access_token(
    current_user: User = Depends(get_current_active_user)
):
    """验证访问令牌"""
    return {
        "valid": True,
        "user_id": str(current_user.id),
        "username": current_user.username
    }
