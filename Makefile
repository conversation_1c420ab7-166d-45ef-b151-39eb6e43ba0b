# Vanna Manager Makefile

.PHONY: help install dev build test clean deploy stop logs backup restore setup docker-build docker-up docker-down

# 默认目标
help:
	@echo "Vanna Manager - 可用命令:"
	@echo ""
	@echo "开发命令:"
	@echo "  install     安装依赖"
	@echo "  dev         启动开发环境"
	@echo "  test        运行测试"
	@echo "  lint        代码检查"
	@echo "  format      代码格式化"
	@echo ""
	@echo "部署命令:"
	@echo "  build       构建Docker镜像"
	@echo "  deploy      部署应用"
	@echo "  stop        停止应用"
	@echo "  restart     重启应用"
	@echo "  logs        查看日志"
	@echo ""
	@echo "维护命令:"
	@echo "  backup      备份数据"
	@echo "  restore     恢复数据"
	@echo "  clean       清理资源"
	@echo "  migrate     数据库迁移"
	@echo ""

# 安装依赖
install:
	@echo "安装后端依赖..."
	cd backend && pip install -r requirements.txt
	@echo "安装前端依赖..."
	cd frontend && npm install
	@echo "依赖安装完成!"

# 项目设置
setup:
	@chmod +x scripts/setup.sh
	@./scripts/setup.sh

# 开发环境
dev:
	@chmod +x scripts/dev.sh
	@./scripts/dev.sh

# 运行测试
test:
	@chmod +x scripts/test.sh
	@./scripts/test.sh

# 清理环境
clean:
	@echo "🧹 清理环境..."
	@rm -rf backend/venv
	@rm -rf backend/__pycache__
	@rm -rf backend/app/__pycache__
	@rm -f backend/vanna_admin.db
	@echo "✅ 清理完成"

# Docker构建
docker-build:
	@echo "🐳 构建Docker镜像..."
	@docker-compose build

# Docker启动
docker-up:
	@echo "🐳 启动Docker服务..."
	@docker-compose up -d

# Docker停止
docker-down:
	@echo "🐳 停止Docker服务..."
	@docker-compose down

# 查看日志
logs:
	@docker-compose logs -f backend

# 查看特定服务日志
logs-backend:
	docker-compose logs -f backend

logs-frontend:
	docker-compose logs -f frontend

logs-celery:
	docker-compose logs -f celery-worker

logs-db:
	docker-compose logs -f postgres

# 备份数据
backup:
	@echo "备份数据..."
	./scripts/deploy.sh backup
	@echo "备份完成!"

# 恢复数据
restore:
	@echo "恢复数据..."
	@read -p "请输入备份目录路径: " backup_path; \
	./scripts/deploy.sh restore $$backup_path
	@echo "恢复完成!"

# 清理资源
clean:
	@echo "清理Docker资源..."
	docker-compose down -v
	docker system prune -f
	docker volume prune -f
	@echo "清理完成!"

# 数据库迁移
migrate:
	@cd backend && source venv/bin/activate && alembic upgrade head

# 创建新的数据库迁移
migration:
	@read -p "请输入迁移描述: " message; \
	docker-compose exec backend alembic revision --autogenerate -m "$$message"

# 进入容器
shell-backend:
	docker-compose exec backend bash

shell-db:
	docker-compose exec postgres psql -U vanna_user -d vanna_manager

shell-redis:
	docker-compose exec redis redis-cli

# 监控
monitor:
	@echo "打开监控界面..."
	@echo "Flower: http://localhost:5555"
	@echo "系统监控: http://localhost:8000/api/v1/system/metrics"

# 健康检查
health:
	@echo "检查服务健康状态..."
	curl -f http://localhost:8000/api/v1/system/health || echo "后端服务异常"
	curl -f http://localhost:3000 || echo "前端服务异常"
	docker-compose ps

# 性能测试
perf-test:
	@echo "运行性能测试..."
	cd backend && locust -f tests/performance/locustfile.py --host=http://localhost:8000

# 安全扫描
security-scan:
	@echo "运行安全扫描..."
	cd backend && bandit -r app/
	cd frontend && npm audit

# 更新依赖
update-deps:
	@echo "更新后端依赖..."
	cd backend && pip-compile requirements.in
	@echo "更新前端依赖..."
	cd frontend && npm update
	@echo "依赖更新完成!"

# 生成API文档
docs:
	@echo "生成API文档..."
	cd backend && python -c "import app.main; print('API文档已生成')"
	@echo "API文档: http://localhost:8000/docs"

# 初始化项目
init:
	@echo "初始化项目..."
	cp .env.example .env
	make install
	make migrate
	@echo "项目初始化完成!"
	@echo "请编辑 .env 文件配置您的环境变量"

# 代码格式化
format:
	@cd backend && source venv/bin/activate && black app/
	@echo "✅ 代码格式化完成"

# 安装pre-commit钩子
install-hooks:
	@cd backend && source venv/bin/activate && pre-commit install
	@echo "✅ Git钩子安装完成"
